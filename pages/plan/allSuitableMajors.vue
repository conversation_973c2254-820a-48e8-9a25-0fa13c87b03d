<template>
	<view class="container">
		<!-- 头部 -->
		<view class="header" :style="'padding-top:' + (titleTop + 10) + 'px'">
			<view class="header-content">
				<view class="back-btn" @tap="back">
					<image :src="imgUrl+'/qdkbm/newimage/fhui/back-light.png'" mode="widthFix"></image>
				</view>
				<view class="header-title">适合专业推荐</view>
				<view class="header-right">
					<view class="more-btn">
						<text>···</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 推荐标签 -->
		<view class="recommendation-tags">
			<view class="tag active">院校优先</view>
			<view class="tag">专业优先</view>
		</view>

		<!-- 统计信息 -->
		<view class="stats-section">
			<view class="stats-header">
				<view class="hot-label">热</view>
				<text class="university-name">{{userInfo.province || '安徽'}}科技大学</text>
				<text class="university-code">代码{{universityCode}}</text>
				<text class="match-rate">匹配 {{matchRate}}%</text>
			</view>
			<view class="stats-numbers">
				<view class="stat-item">
					<text class="stat-number total">全部 {{totalCount}}</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">冲 {{rushCount}}</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">稳 {{stableCount}}</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">保 {{safeCount}}</text>
				</view>
			</view>
		</view>

		<!-- 筛选栏 -->
		<view class="filter-bar">
			<view class="filter-item" @tap="showFilterPopup('school')">
				<text>院校</text>
				<image :src="imgUrl+'/qdkbm/newimage/fhui/arrow-down.png'" mode="widthFix"></image>
			</view>
			<view class="filter-item" @tap="showFilterPopup('major')">
				<text>专业</text>
				<image :src="imgUrl+'/qdkbm/newimage/fhui/arrow-down.png'" mode="widthFix"></image>
			</view>
			<view class="filter-item" @tap="showFilterPopup('job')">
				<text>职业</text>
				<image :src="imgUrl+'/qdkbm/newimage/fhui/arrow-down.png'" mode="widthFix"></image>
			</view>
			<view class="filter-item" @tap="showFilterPopup('sort')">
				<text>排序</text>
				<image :src="imgUrl+'/qdkbm/newimage/fhui/arrow-down.png'" mode="widthFix"></image>
			</view>
			<view class="search-btn" @tap="handleSearch">
				<image :src="imgUrl+'/qdkbm/newimage/fhui/search.png'" mode="widthFix"></image>
			</view>
		</view>

		<!-- 专业列表 -->
		<view class="major-list">
			<view class="major-item" v-for="(item, index) in majorList" :key="index" @tap="viewMajorDetail(item)">
				<view class="major-header">
					<view class="school-info">
						<view class="school-logo">
							<image :src="item.schoolLogo || defaultLogo" mode="aspectFit"></image>
						</view>
						<view class="school-details">
							<view class="school-name">{{item.schoolName}}</view>
							<view class="school-meta">
								<text class="school-location">{{item.province}} {{item.schoolType}}</text>
								<text class="major-code">{{item.majorCode}}</text>
							</view>
						</view>
					</view>
					<view class="recommendation-badge" v-if="item.isRecommended">
						<text class="badge-text">{{item.recommendLevel}}%</text>
						<text class="badge-label">冲</text>
					</view>
				</view>

				<view class="major-content">
					<view class="major-name">{{item.majorName}}</view>
					<view class="major-group">{{item.majorGroup}}</view>

					<view class="major-stats">
						<view class="stat-row">
							<text class="stat-label">年份</text>
							<text class="stat-label">招生</text>
							<text class="stat-label">最低分</text>
							<text class="stat-label">最低位次</text>
							<text class="stat-label">专业分</text>
						</view>
						<view class="stat-row" v-for="(year, yearIndex) in item.yearData" :key="yearIndex">
							<text class="stat-value">{{year.year}}</text>
							<text class="stat-value">{{year.enrollment}}人</text>
							<text class="stat-value">{{year.minScore}}</text>
							<text class="stat-value">{{year.minRank}}</text>
							<text class="stat-value">{{year.majorScore}}</text>
						</view>
					</view>
				</view>

				<view class="major-footer">
					<view class="major-tags">
						<text class="tag-item" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{tag}}</text>
					</view>
					<view class="action-btn" @tap.stop="toggleFavorite(item)">
						<text>可填专业 {{item.fillableCount}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载更多 -->
		<view class="load-more" v-if="hasMore" @tap="loadMore">
			<text>加载更多</text>
		</view>

		<!-- 加载中 -->
		<view class="loading-mask" v-if="loading">
			<view class="loading-container">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	// 启用下拉刷新
	onPullDownRefresh() {
		this.refreshData();
	},

	data() {
		return {
			imgUrl: this.$base.uploadImgUrl,
			titleTop: 0,
			loading: false,
			hasMore: true,
			pageNo: 1,
			pageSize: 10,

			// 用户信息
			userInfo: {},

			// 统计数据
			universityCode: '9005',
			matchRate: 96,
			totalCount: 568,
			rushCount: 136,
			stableCount: 211,
			safeCount: 221,

			// 专业列表
			majorList: [],

			// 默认logo
			defaultLogo: '',

			// 问卷答案数据
			questionData: null,

			// 筛选条件
			filterConditions: {
				school: '',
				major: '',
				job: '',
				sort: ''
			}
		}
	},

	onLoad(options) {
		// 获取传递的问卷数据
		if (options.questionData) {
			try {
				this.questionData = JSON.parse(decodeURIComponent(options.questionData));
			} catch (e) {
				console.error('解析问卷数据失败:', e);
			}
		}

		// 获取报告ID
		if (options.reportId) {
			this.reportId = options.reportId;
		}
	},

	onReady() {
		let res = uni.getMenuButtonBoundingClientRect();
		this.titleTop = res.top;
	},

	onShow() {
		this.getUserInfo();
		this.getMajorData();
	},

	methods: {
		// 获取用户信息
		getUserInfo() {
			const userInfo = this.$store.state.userInfo;
			if (userInfo) {
				this.userInfo = userInfo;
			}
		},

		// 获取专业数据
		getMajorData() {
			if (!this.questionData) {
				// 如果没有问卷数据，尝试从报告ID获取
				this.getQuestionDataFromReport();
				return;
			}

			// 提取 userAnswers 对象
			let userAnswers = null;
			if (this.questionData.question && this.questionData.question.userAnswers) {
				userAnswers = this.questionData.question.userAnswers;
			} else if (this.questionData.userAnswers) {
				userAnswers = this.questionData.userAnswers;
			} else {
				console.error('无法找到 userAnswers 数据:', this.questionData);
				uni.showToast({
					title: '问卷数据格式错误',
					icon: 'none'
				});
				// 使用模拟数据
				this.loadMockData();
				return;
			}

			console.log('准备调用接口，userAnswers:', userAnswers);

			this.loading = true;

			// 调用接口时只传递 userAnswers 对象
			this.$apis.getAllSuitableMajors({
				userAnswers: userAnswers
			}).then((res) => {
				this.loading = false;
				console.log('接口返回结果:', res);
				if (res.code === 0 && res.data) {
					this.processMajorData(res.data);
				} else {
					console.error('接口返回错误:', res);
					uni.showToast({
						title: res.msg || '获取专业数据失败',
						icon: 'none'
					});
					// 使用模拟数据
					this.loadMockData();
				}
			}).catch((err) => {
				this.loading = false;
				console.error('获取专业数据失败:', err);
				uni.showToast({
					title: '网络错误，请稍后重试',
					icon: 'none'
				});
				// 使用模拟数据
				this.loadMockData();
			});
		},

		// 从报告ID获取问卷数据
		getQuestionDataFromReport() {
			if (!this.reportId) {
				uni.showToast({
					title: '缺少必要参数',
					icon: 'none'
				});
				return;
			}

			// 这里需要调用获取问卷答案的接口
			this.$apis.getRecordAnswer({
				answerRecordId: this.reportId
			}).then((res) => {
				if (res.code === 0 && res.data) {
					this.questionData = res.data;
					this.getMajorData();
				}
			}).catch((err) => {
				console.error('获取问卷数据失败:', err);
			});
		},

		// 处理专业数据
		processMajorData(data) {
			console.log('处理专业数据:', data);

			// 更新统计数据
			if (data.totalCount !== undefined) {
				this.totalCount = data.totalCount;
			}
			if (data.rushCount !== undefined) {
				this.rushCount = data.rushCount;
			}
			if (data.stableCount !== undefined) {
				this.stableCount = data.stableCount;
			}
			if (data.safeCount !== undefined) {
				this.safeCount = data.safeCount;
			}

			// 更新用户信息
			if (data.userProfile) {
				this.userProfile = data.userProfile;
				// 更新省份信息
				if (data.userProfile.province) {
					this.userInfo.province = data.userProfile.province;
				}
			}

			// 合并所有专业数据
			let allMajors = [];

			// 添加冲刺专业
			if (data.rushMajors && Array.isArray(data.rushMajors)) {
				allMajors = allMajors.concat(data.rushMajors.map(item => ({
					...item,
					category: '冲',
					categoryColor: '#FF6B6B',
					schoolName: item.SchoolName,
					majorName: item.MajorName,
					majorCode: item.MajorCode,
					province: item.ProvinceName,
					schoolType: item.BatchName,
					minScore: item.LowestScore,
					minRank: item.LowestSection,
					year: item.Year,
					typeName: item.TypeName,
					proScore: item.ProScore,
					averageScore: item.AverageScore,
					highScore: item.HighSocre,
					majorStandardCode: item.MajorStandardCode,
					schoolUUID: item.SchoolUUID,
					enrollmentPlan: item.enrollmentPlanData || [],
					isRecommended: true,
					recommendLevel: 20
				})));
			}

			// 添加稳妥专业
			if (data.stableMajors && Array.isArray(data.stableMajors)) {
				allMajors = allMajors.concat(data.stableMajors.map(item => ({
					...item,
					category: '稳',
					categoryColor: '#4ECDC4',
					schoolName: item.SchoolName,
					majorName: item.MajorName,
					majorCode: item.MajorCode,
					province: item.ProvinceName,
					schoolType: item.BatchName,
					minScore: item.LowestScore,
					minRank: item.LowestSection,
					year: item.Year,
					typeName: item.TypeName,
					proScore: item.ProScore,
					averageScore: item.AverageScore,
					highScore: item.HighSocre,
					majorStandardCode: item.MajorStandardCode,
					schoolUUID: item.SchoolUUID,
					enrollmentPlan: item.enrollmentPlanData || [],
					isRecommended: true,
					recommendLevel: 15
				})));
			}

			// 添加保底专业
			if (data.safeMajors && Array.isArray(data.safeMajors)) {
				allMajors = allMajors.concat(data.safeMajors.map(item => ({
					...item,
					category: '保',
					categoryColor: '#45B7D1',
					schoolName: item.SchoolName,
					majorName: item.MajorName,
					majorCode: item.MajorCode,
					province: item.ProvinceName,
					schoolType: item.BatchName,
					minScore: item.LowestScore,
					minRank: item.LowestSection,
					year: item.Year,
					typeName: item.TypeName,
					proScore: item.ProScore,
					averageScore: item.AverageScore,
					highScore: item.HighSocre,
					majorStandardCode: item.MajorStandardCode,
					schoolUUID: item.SchoolUUID,
					enrollmentPlan: item.enrollmentPlanData || [],
					isRecommended: false,
					recommendLevel: 10
				})));
			}

			// 为每个专业添加标签和其他信息
			this.majorList = allMajors.map(item => ({
				...item,
				id: item.MajorCode || item.majorCode,
				tags: this.generateTags(item),
				fillableCount: item.enrollmentPlan ? item.enrollmentPlan.length : 1,
				yearData: this.generateYearData(item)
			}));

			console.log('处理后的专业列表:', this.majorList);
		},

		// 加载模拟数据
		loadMockData() {
			this.majorList = [
				{
					id: 1,
					schoolName: '东北师范大学',
					schoolLogo: '',
					province: '吉林',
					schoolType: '公办 师范',
					majorCode: '211',
					majorName: '专业组',
					majorGroup: '[007]专业组',
					isRecommended: true,
					recommendLevel: 20,
					yearData: [
						{ year: 2024, enrollment: '7人', minScore: 597, minRank: 27577, majorScore: 597 },
						{ year: 2023, enrollment: '10人', minScore: 537, minRank: 51819, majorScore: 563 },
						{ year: 2022, enrollment: '11人', minScore: 587, minRank: 16532, majorScore: 617 }
					],
					tags: ['安徽', '物化生', '586分', '34696名'],
					fillableCount: 4
				},
				{
					id: 2,
					schoolName: '上海理工大学',
					schoolLogo: '',
					province: '上海',
					schoolType: '公办 理工',
					majorCode: '002',
					majorName: '专业组',
					majorGroup: '[002]专业组',
					isRecommended: true,
					recommendLevel: 20,
					yearData: [
						{ year: 2024, enrollment: '14人', minScore: 0, minRank: 0, majorScore: 0 },
						{ year: 2023, enrollment: '0人', minScore: 0, minRank: 0, majorScore: 0 },
						{ year: 2022, enrollment: '0人', minScore: 0, minRank: 0, majorScore: 0 }
					],
					tags: ['安徽', '物化生', '586分', '34696名'],
					fillableCount: 4
				}
			];
		},

		// 显示筛选弹窗
		showFilterPopup(type) {
			// 实现筛选功能
			console.log('显示筛选:', type);
		},

		// 搜索
		handleSearch() {
			// 实现搜索功能
			console.log('搜索');
		},

		// 查看专业详情
		viewMajorDetail(item) {
			uni.navigateTo({
				url: `/pages/plan/majorDetail?majorId=${item.id}&majorName=${encodeURIComponent(item.majorName)}`
			});
		},

		// 切换收藏
		toggleFavorite(item) {
			// 实现收藏功能
			console.log('切换收藏:', item);
		},

		// 加载更多
		loadMore() {
			if (this.loading || !this.hasMore) return;

			this.pageNo++;
			this.getMajorData();
		},

		// 下拉刷新
		refreshData() {
			this.pageNo = 1;
			this.hasMore = true;
			this.majorList = [];
			this.getMajorData();

			// 停止下拉刷新动画
			setTimeout(() => {
				uni.stopPullDownRefresh();
			}, 1000);
		},

		// 返回
		back() {
			uni.navigateBack({
				delta: 1
			});
		}
	}
}
</script>

<style scoped lang="scss">
page {
	background: #f5f5f5;
}

.container {
	background: #f5f5f5;
	min-height: 100vh;
}

.header {
	background: #fff;
	border-bottom: 1rpx solid #eee;

	.header-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;

		.back-btn {
			width: 40rpx;
			height: 40rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.header-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
		}

		.header-right {
			width: 40rpx;

			.more-btn {
				font-size: 32rpx;
				color: #666;
			}
		}
	}
}

.recommendation-tags {
	display: flex;
	padding: 20rpx 30rpx;
	background: #fff;
	border-bottom: 1rpx solid #eee;

	.tag {
		padding: 16rpx 32rpx;
		border-radius: 24rpx;
		font-size: 28rpx;
		margin-right: 20rpx;
		background: #f5f5f5;
		color: #666;
		font-weight: 500;
		transition: all 0.3s ease;

		&.active {
			background: linear-gradient(135deg, #FF8918 0%, #FF6B18 100%);
			color: #fff;
			box-shadow: 0 4rpx 12rpx rgba(255, 137, 24, 0.3);
		}
	}
}

.stats-section {
	background: #fff;
	padding: 20rpx 30rpx;
	margin-bottom: 20rpx;

	.stats-header {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;

		.hot-label {
			background: #FF4444;
			color: #fff;
			padding: 4rpx 8rpx;
			border-radius: 6rpx;
			font-size: 24rpx;
			margin-right: 12rpx;
		}

		.university-name {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-right: 12rpx;
		}

		.university-code {
			font-size: 24rpx;
			color: #666;
			margin-right: 12rpx;
		}

		.match-rate {
			font-size: 24rpx;
			color: #FF8918;
		}
	}

	.stats-numbers {
		display: flex;

		.stat-item {
			margin-right: 40rpx;

			.stat-number {
				font-size: 28rpx;
				color: #333;

				&.total {
					color: #FF8918;
					font-weight: bold;
				}
			}
		}
	}
}

.filter-bar {
	display: flex;
	align-items: center;
	background: #fff;
	padding: 20rpx 30rpx;
	margin-bottom: 20rpx;

	.filter-item {
		display: flex;
		align-items: center;
		margin-right: 40rpx;
		font-size: 28rpx;
		color: #333;

		image {
			width: 20rpx;
			height: 20rpx;
			margin-left: 8rpx;
		}
	}

	.search-btn {
		margin-left: auto;

		image {
			width: 32rpx;
			height: 32rpx;
		}
	}
}

.major-list {
	padding: 0 30rpx;
}

.major-item {
	background: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}

.major-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 20rpx;

	.school-info {
		display: flex;
		align-items: center;
		flex: 1;

		.school-logo {
			width: 60rpx;
			height: 60rpx;
			margin-right: 20rpx;

			image {
				width: 100%;
				height: 100%;
				border-radius: 8rpx;
			}
		}

		.school-details {
			.school-name {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 8rpx;
			}

			.school-meta {
				display: flex;
				align-items: center;

				.school-location {
					font-size: 24rpx;
					color: #666;
					margin-right: 20rpx;
				}

				.major-code {
					font-size: 24rpx;
					color: #666;
				}
			}
		}
	}

	.recommendation-badge {
		background: #FF8918;
		color: #fff;
		padding: 8rpx 16rpx;
		border-radius: 8rpx;
		text-align: center;

		.badge-text {
			font-size: 24rpx;
			font-weight: bold;
		}

		.badge-label {
			font-size: 20rpx;
		}
	}
}

.major-content {
	margin-bottom: 20rpx;

	.major-name {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 8rpx;
	}

	.major-group {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 20rpx;
	}
}

.major-stats {
	.stat-row {
		display: flex;
		justify-content: space-between;
		padding: 8rpx 0;
		border-bottom: 1rpx solid #f5f5f5;

		&:last-child {
			border-bottom: none;
		}

		.stat-label {
			font-size: 24rpx;
			color: #666;
			flex: 1;
			text-align: center;
		}

		.stat-value {
			font-size: 24rpx;
			color: #333;
			flex: 1;
			text-align: center;
		}
	}
}

.major-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;

	.major-tags {
		display: flex;
		flex-wrap: wrap;

		.tag-item {
			background: #f5f5f5;
			color: #666;
			padding: 4rpx 12rpx;
			border-radius: 8rpx;
			font-size: 20rpx;
			margin-right: 12rpx;
			margin-bottom: 8rpx;
		}
	}

	.action-btn {
		background: #FF8918;
		color: #fff;
		padding: 12rpx 24rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
	}
}

.load-more {
	text-align: center;
	padding: 40rpx;
	color: #666;
	font-size: 28rpx;
}

.loading-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;

	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		background: rgba(255, 255, 255, 0.9);
		border-radius: 16rpx;
		padding: 30rpx 40rpx;

		.loading-spinner {
			width: 70rpx;
			height: 70rpx;
			animation: spin 1s linear infinite;
			background-image: url('https://pic.kefeichangduo.top/qdkbm/newimage/fhui/waiting.png');
			background-size: contain;
			background-repeat: no-repeat;
		}

		.loading-text {
			margin-top: 20rpx;
			font-size: 28rpx;
			color: #AA7248;
		}
	}
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
</style>
