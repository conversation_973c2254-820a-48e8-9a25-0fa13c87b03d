{"code": 0, "data": {"userProfile": {"province": "辽宁", "year": 2024, "gender": "女", "subjects": ["历史", "生物", "政治"], "totalScore": 538, "subjectScores": null, "personalityTraits": [], "familyIncome": null, "graduationPlan": null, "interestedMajorCategories": ["护理学"], "preferredLocation": null, "careerDirection": "体制内", "typeName": "历史类"}, "rushMajors": [{"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "南昌大学", "EnrollmentNumbers": 5, "SchoolUUID": "27e68391391a03ab307a88beb470cef5", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（建议身高要求男生167cm、女生157cm以上）", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "南昌大学", "EnrollmentNumbers": 5, "SchoolUUID": "27e68391391a03ab307a88beb470cef5", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101K", "Year": 2023, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "27e68391391a03ab307a88beb470cef5", "SchoolName": "南昌大学", "MajorName": "护理学", "MajorCode": 4988, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "552", "LowestSection": "5852", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": null, "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "aeb01c53472ac556fdddbe23c150d502", "SchoolName": "哈尔滨医科大学", "MajorName": "护理学（大庆校区）", "MajorCode": 22739, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "558", "LowestSection": "5238", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "天津中医药大学", "EnrollmentNumbers": 1, "SchoolUUID": "732cc88e68db53f69826f47e1316e6c0", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学类（护理学、护理学（涉外英语班）、护理学（老年护理班）、护理学（中医护理））（外语语种要求参考（天津中医药大学2024年普通本科招生章程）第十六条）", "CollegeMajorCode": "1011", "Year": 2024, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "732cc88e68db53f69826f47e1316e6c0", "SchoolName": "天津中医药大学", "MajorName": "护理学类（护理学、护理学（涉外英语班）、护理学（老年护理班）、护理学（中医护理））", "MajorCode": 145328, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "560", "LowestSection": "5051", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": ""}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "哈尔滨医科大学", "EnrollmentNumbers": 3, "SchoolUUID": "aeb01c53472ac556fdddbe23c150d502", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（一二年级在大庆校区，三四年级在校本部）（不招收色盲、色弱考生）", "CollegeMajorCode": "101101K", "Year": 2023, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "哈尔滨医科大学", "EnrollmentNumbers": 3, "SchoolUUID": "aeb01c53472ac556fdddbe23c150d502", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（一二年级在大庆校区,三四年级在校本部）（色盲色弱不能报考）", "CollegeMajorCode": "101101K", "Year": 2022, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "aeb01c53472ac556fdddbe23c150d502", "SchoolName": "哈尔滨医科大学", "MajorName": "护理学", "MajorCode": 22739, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "568", "LowestSection": "4333", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "天津医科大学临床医学院", "EnrollmentNumbers": 2, "SchoolUUID": "69646e5a5ddb5050569dd2532248c4db", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "天津医科大学", "EnrollmentNumbers": 1, "SchoolUUID": "a3e4ea7017ba0d8bc1b3a9055bdf3c41", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（色盲、色弱考生不予录取）", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "天津医科大学", "EnrollmentNumbers": 1, "SchoolUUID": "a3e4ea7017ba0d8bc1b3a9055bdf3c41", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（色盲、色弱考生不予录取）", "CollegeMajorCode": "101101", "Year": 2023, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "天津医科大学", "EnrollmentNumbers": 1, "SchoolUUID": "a3e4ea7017ba0d8bc1b3a9055bdf3c41", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（色盲、色弱考生不予录取）", "CollegeMajorCode": "101101K", "Year": 2023, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "天津医科大学", "EnrollmentNumbers": 1, "SchoolUUID": "a3e4ea7017ba0d8bc1b3a9055bdf3c41", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（色盲、色弱考生不予录取）", "CollegeMajorCode": "101101K", "Year": 2022, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "a3e4ea7017ba0d8bc1b3a9055bdf3c41", "SchoolName": "天津医科大学", "MajorName": "护理学", "MajorCode": 3058, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "587", "LowestSection": "2813", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}], "stableMajors": [{"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "河南中医药大学", "EnrollmentNumbers": 2, "SchoolUUID": "178cd03f23166c9d3e337925b3092f4f", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "河南中医药大学", "EnrollmentNumbers": 2, "SchoolUUID": "178cd03f23166c9d3e337925b3092f4f", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101", "Year": 2023, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "河南中医药大学", "EnrollmentNumbers": 2, "SchoolUUID": "178cd03f23166c9d3e337925b3092f4f", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101K", "Year": 2023, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "河南中医药大学", "EnrollmentNumbers": 2, "SchoolUUID": "178cd03f23166c9d3e337925b3092f4f", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101", "Year": 2022, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "河南中医药大学", "EnrollmentNumbers": 2, "SchoolUUID": "178cd03f23166c9d3e337925b3092f4f", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101K", "Year": 2022, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "178cd03f23166c9d3e337925b3092f4f", "SchoolName": "河南中医药大学", "MajorName": "护理学", "MajorCode": 40242, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "531", "LowestSection": "8143", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "河南中医药大学", "EnrollmentNumbers": 2, "SchoolUUID": "178cd03f23166c9d3e337925b3092f4f", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "河南中医药大学", "EnrollmentNumbers": 2, "SchoolUUID": "178cd03f23166c9d3e337925b3092f4f", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101", "Year": 2023, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "河南中医药大学", "EnrollmentNumbers": 2, "SchoolUUID": "178cd03f23166c9d3e337925b3092f4f", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101K", "Year": 2023, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "河南中医药大学", "EnrollmentNumbers": 2, "SchoolUUID": "178cd03f23166c9d3e337925b3092f4f", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101", "Year": 2022, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "河南中医药大学", "EnrollmentNumbers": 2, "SchoolUUID": "178cd03f23166c9d3e337925b3092f4f", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101K", "Year": 2022, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "178cd03f23166c9d3e337925b3092f4f", "SchoolName": "河南中医药大学", "MajorName": "护理学", "MajorCode": 40242, "Year": 2024, "HighSocre": "532", "AverageScore": "-", "LowestScore": "531", "LowestSection": "8143", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "河北医科大学", "EnrollmentNumbers": 4, "SchoolUUID": "0d564e37e3593ac2e0bc065328eb7474", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学类（护理学、助产学）（注意查看（招生章程）中身体条件）", "CollegeMajorCode": "1011", "Year": 2024, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "河北医科大学", "EnrollmentNumbers": 4, "SchoolUUID": "0d564e37e3593ac2e0bc065328eb7474", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学类（护理学、助产学）（注意查看《招生章程》中身体条件）", "CollegeMajorCode": "1011", "Year": 2023, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "0d564e37e3593ac2e0bc065328eb7474", "SchoolName": "河北医科大学", "MajorName": "护理学类（护理学、助产学）", "MajorCode": 1499869, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "538", "LowestSection": "7379", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": ""}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "沈阳医学院", "EnrollmentNumbers": 1, "SchoolUUID": "c0a48577365c580a4dc9616cf54899ab", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（外语只用英语授课，不招收色盲色弱考生）", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "沈阳医学院", "EnrollmentNumbers": 11, "SchoolUUID": "c0a48577365c580a4dc9616cf54899ab", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（外语只用英语授课，不招收色盲色弱考生）", "CollegeMajorCode": "101101K", "Year": 2023, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "c0a48577365c580a4dc9616cf54899ab", "SchoolName": "沈阳医学院", "MajorName": "护理学", "MajorCode": 10411, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "544", "LowestSection": "6726", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}], "safeMajors": [{"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "北京中医药大学东方学院", "EnrollmentNumbers": 3, "SchoolUUID": "bccf3517b8e41b871089efe2510802f5", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（不招色觉异常（含色盲、色弱）考生）", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "bccf3517b8e41b871089efe2510802f5", "SchoolName": "北京中医药大学东方学院", "MajorName": "护理学", "MajorCode": 1727620, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "448", "LowestSection": "18570", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "新疆科技学院", "EnrollmentNumbers": 5, "SchoolUUID": "050c2c6344a290b1f557a5d01c0a849c", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "050c2c6344a290b1f557a5d01c0a849c", "SchoolName": "新疆科技学院", "MajorName": "护理学", "MajorCode": 611714, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "451", "LowestSection": "18176", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "昆明医科大学海源学院", "EnrollmentNumbers": 1, "SchoolUUID": "85f55aef72d3871f59956cf189c4cf3d", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（色盲、色弱不录）", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "85f55aef72d3871f59956cf189c4cf3d", "SchoolName": "昆明医科大学海源学院", "MajorName": "护理学（色盲、色弱不录）", "MajorCode": 1688157, "Year": 2024, "HighSocre": "456", "AverageScore": "456", "LowestScore": "456", "LowestSection": "17553", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "昆明医科大学海源学院", "EnrollmentNumbers": 1, "SchoolUUID": "85f55aef72d3871f59956cf189c4cf3d", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（色盲、色弱不录）", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "85f55aef72d3871f59956cf189c4cf3d", "SchoolName": "昆明医科大学海源学院", "MajorName": "护理学", "MajorCode": 1688157, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "456", "LowestSection": "17553", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "河北医科大学临床学院", "EnrollmentNumbers": 5, "SchoolUUID": "7b2b0f516a61f2ae3153673df051f8b2", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（注意查看（招生章程）中身体条件）", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "7b2b0f516a61f2ae3153673df051f8b2", "SchoolName": "河北医科大学临床学院", "MajorName": "护理学", "MajorCode": 1687138, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "460", "LowestSection": "17023", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "齐鲁医药学院", "EnrollmentNumbers": 2, "SchoolUUID": "550ea77b1bd86851613c7b6a49524609", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "550ea77b1bd86851613c7b6a49524609", "SchoolName": "齐鲁医药学院", "MajorName": "护理学", "MajorCode": 72000, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "469", "LowestSection": "15810", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "宜春学院", "EnrollmentNumbers": 1, "SchoolUUID": "37505dc78553f8bbbb73eb04671112c7", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（辅修医学美容技术，在美容医学院就读）", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "37505dc78553f8bbbb73eb04671112c7", "SchoolName": "宜春学院", "MajorName": "护理学", "MajorCode": 9956, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "489", "LowestSection": "13255", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "大理大学", "EnrollmentNumbers": 2, "SchoolUUID": "f33e96e11511de8b7859a3e9b45602af", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（色弱色盲不录）", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "f33e96e11511de8b7859a3e9b45602af", "SchoolName": "大理大学", "MajorName": "护理学", "MajorCode": 1744067, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "498", "LowestSection": "12131", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "大连大学", "EnrollmentNumbers": 4, "SchoolUUID": "925ee3456bed80277f968de8363f459e", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "925ee3456bed80277f968de8363f459e", "SchoolName": "大连大学", "MajorName": "护理学", "MajorCode": 11103, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "504", "LowestSection": "11341", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "西安医学院", "EnrollmentNumbers": 4, "SchoolUUID": "45e4d0caead9ed5aa0c12ee905e147e5", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "45e4d0caead9ed5aa0c12ee905e147e5", "SchoolName": "西安医学院", "MajorName": "护理学", "MajorCode": 52265, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "505", "LowestSection": "11211", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "南昌医学院", "EnrollmentNumbers": 1, "SchoolUUID": "508cd1c966f12d9eee01c1ac7fe0918e", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（不招色盲色弱考生，建议男生身高不低于167CM，女生身高不低于157CM）", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "508cd1c966f12d9eee01c1ac7fe0918e", "SchoolName": "南昌医学院", "MajorName": "护理学", "MajorCode": 78599, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "512", "LowestSection": "10381", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "山西中医药大学", "EnrollmentNumbers": 2, "SchoolUUID": "26f3db7d99d1e6e0bd846ceef33871a2", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（不招收色盲、色弱考生）", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "山西中医药大学", "EnrollmentNumbers": 2, "SchoolUUID": "26f3db7d99d1e6e0bd846ceef33871a2", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（不招收色盲、色弱考生）", "CollegeMajorCode": "101101K", "Year": 2023, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "山西中医药大学", "EnrollmentNumbers": 1, "SchoolUUID": "26f3db7d99d1e6e0bd846ceef33871a2", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101", "Year": 2022, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "山西中医药大学", "EnrollmentNumbers": 1, "SchoolUUID": "26f3db7d99d1e6e0bd846ceef33871a2", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101K", "Year": 2022, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "26f3db7d99d1e6e0bd846ceef33871a2", "SchoolName": "山西中医药大学", "MajorName": "护理学", "MajorCode": 37329, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "512", "LowestSection": "10381", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "黑龙江中医药大学", "EnrollmentNumbers": 3, "SchoolUUID": "9a7de6982f9d625aa3879001e019b631", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（所属学院为佳木斯学院，授课地点在佳木斯市）", "CollegeMajorCode": "101101K", "Year": 2023, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "黑龙江中医药大学", "EnrollmentNumbers": 3, "SchoolUUID": "9a7de6982f9d625aa3879001e019b631", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（所属学院为佳木斯学院,授课地点在佳木斯市）", "CollegeMajorCode": "101101K", "Year": 2022, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "9a7de6982f9d625aa3879001e019b631", "SchoolName": "黑龙江中医药大学", "MajorName": "护理学", "MajorCode": 23449, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "514", "LowestSection": "10144", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}, {"historicalData": null, "historicalDataLoading": false, "historicalDataLoadId": null, "enrollmentPlanData": [{"InSchoolYears": "4", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "山西医科大学", "EnrollmentNumbers": 3, "SchoolUUID": "1a895270fe4c4d21749fd18120552226", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学", "CollegeMajorCode": "101101K", "Year": 2024, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "山西医科大学", "EnrollmentNumbers": 3, "SchoolUUID": "1a895270fe4c4d21749fd18120552226", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（外语语种为英语）", "CollegeMajorCode": "101101K", "Year": 2023, "ProvinceName": "辽宁"}, {"InSchoolYears": "四年", "ClassOne": "医学", "ClassTwo": "护理学类", "BatchName": "本科批", "Type": "历史类", "SchoolName": "山西医科大学", "EnrollmentNumbers": 2, "SchoolUUID": "1a895270fe4c4d21749fd18120552226", "CourseSelectionRequirements": "", "CollegeMajorName": "护理学（就读于汾阳学院）（外语语种为英语）", "CollegeMajorCode": "101101K", "Year": 2023, "ProvinceName": "辽宁"}], "enrollmentPlanDataLoading": false, "ProvinceName": "辽宁", "SchoolUUID": "1a895270fe4c4d21749fd18120552226", "SchoolName": "山西医科大学", "MajorName": "护理学", "MajorCode": 144722, "Year": 2024, "HighSocre": "-", "AverageScore": "-", "LowestScore": "526", "LowestSection": "8687", "BatchName": "本科批", "TypeName": "历史类", "ProScore": "400", "subjectSelection": null, "MajorStandardCode": "101101K"}], "rushCount": 5, "stableCount": 4, "safeCount": 14, "totalCount": 23, "queryInfo": "查询条件：\n• 省份：辽宁\n• 年份：2024\n• 选科：历史、生物、政治\n• 分数：538分\n• 分数范围：438-588分\n• 意向专业：护理学\n\n查询结果（按冲稳保分类）：\n• 冲刺专业：5个（分数高于548分）\n• 稳妥专业：4个（分数在528-548分范围内）\n• 保底专业：14个（分数低于528分）\n• 专业总数：23个\n• 所有专业均符合您的意向专业要求\n• 所有专业均符合您的选科要求\n• 已查询近三年（2022-2024）招生计划数据\n\n冲刺专业分数范围：552-587分\n稳妥专业分数范围：531-544分\n保底专业分数范围：448-526分\n"}, "msg": ""}